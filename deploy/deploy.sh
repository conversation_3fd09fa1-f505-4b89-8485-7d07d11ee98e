#!/bin/bash
set -e

# Mono Banking MCP Server - Deployment Script
# Run this on your EC2 instance to deploy/update the application

echo "🚀 Deploying Mono Banking MCP Server..."

# Navigate to application directory
cd /opt/mono-mcp

# Pull latest changes
echo "📥 Pulling latest code..."
git pull origin main

# Build and deploy with Docker Compose
echo "🐳 Building and starting containers..."
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 30

# Check service status
echo "🔍 Checking service status..."
docker-compose ps

# Show recent logs
echo "📝 Recent logs:"
docker-compose logs --tail=50

# Test health endpoints
echo "🏥 Testing health endpoints..."
curl -f http://localhost:8000/health || echo "❌ Health check failed"

echo "✅ Deployment complete!"
echo ""
echo "📊 Service URLs:"
echo "   - MCP Server: http://$(curl -s http://***************/latest/meta-data/public-ipv4):3000"
echo "   - Webhook Server: http://$(curl -s http://***************/latest/meta-data/public-ipv4):8000"
echo ""
echo "📝 Useful commands:"
echo "   - View logs: docker-compose logs -f"
echo "   - Restart: docker-compose restart"
echo "   - Stop: docker-compose down"
echo "   - Update: ./deploy/deploy.sh"
