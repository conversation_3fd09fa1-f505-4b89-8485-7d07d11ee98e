#!/bin/bash
set -e

# Mono Banking MCP Server - EC2 Instance Setup Script
# Run this script on a fresh Ubuntu EC2 instance

echo "🚀 Setting up Mono Banking MCP Server on EC2..."

# Update system
sudo apt-get update && sudo apt-get upgrade -y

# Install Docker
echo "📦 Installing Docker..."
sudo apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
sudo apt-get update
sudo apt-get install -y docker-ce docker-ce-cli containerd.io

# Install Docker Compose
echo "🐳 Installing Docker Compose..."
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add ubuntu user to docker group
sudo usermod -aG docker ubuntu

# Install git and other utilities
sudo apt-get install -y git htop curl unzip

# Create application directory
sudo mkdir -p /opt/mono-mcp
sudo chown ubuntu:ubuntu /opt/mono-mcp

# Clone repository (replace with your actual repo URL)
cd /opt/mono-mcp
git clone https://github.com/sin4ch/mono-mcp.git .

# Create environment file template
cat > .env << 'EOF'
# Mono API Configuration
MONO_SECRET_KEY=your_secret_key_here
MONO_PUBLIC_KEY=your_public_key_here
MONO_WEBHOOK_SECRET=your_webhook_secret_here
MONO_BASE_URL=https://api.withmono.com
MONO_ENVIRONMENT=production

# Database Configuration
DATABASE_PATH=/app/data/mono_banking.db

# Security
ALLOWED_HOSTS=your-domain.com,your-ip-address
EOF

echo "✅ EC2 setup complete!"
echo ""
echo "📝 Next steps:"
echo "1. Edit /opt/mono-mcp/.env with your actual Mono API credentials"
echo "2. Update nginx.conf with your domain name"
echo "3. Run: cd /opt/mono-mcp && docker-compose up -d"
echo "4. Check logs: docker-compose logs -f"
echo ""
echo "🔗 Your server will be available at:"
echo "   - MCP Server: http://your-ip:3000"
echo "   - Webhook Server: http://your-ip:8000"
echo "   - Nginx Proxy: http://your-ip:80"
