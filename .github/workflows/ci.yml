name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v4
        with:
          python-version: 3.12

      - name: Install uv
        run: pip install uv

      - name: Install project and dependencies
        run: |
          uv venv
          uv pip install -e ".[dev]"

      - name: Run tests
        run: uv run pytest tests/ -v
