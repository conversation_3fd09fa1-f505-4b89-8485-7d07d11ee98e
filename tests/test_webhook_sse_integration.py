"""Test the webhook to SSE integration."""
import asyncio
import json
import pytest
import os
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch

from mono_banking_mcp.webhook_server import app as webhook_app, event_queue
from mono_banking_mcp.server import app as mcp_app


class TestWebhookSSEIntegration:
    def test_webhook_queues_event(self):
        """Test that webhook endpoint queues events properly."""
        client = TestClient(webhook_app)
        
        # Mock the webhook signature verification
        with patch('mono_banking_mcp.webhook_server.verify_webhook_signature', return_value=True):
            with patch.dict(os.environ, {'MONO_WEBHOOK_SECRET': 'test-secret'}):
                # Send a test webhook
                webhook_data = {
                    "event": "mono.events.account_updated",
                    "timestamp": "2024-01-01T00:00:00Z",
                    "data": {
                        "account": {
                            "id": "test-account-id",
                            "name": "Test Account"
                        }
                    }
                }
                
                response = client.post(
                    "/mono/webhook",
                    json=webhook_data,
                    headers={"mono-webhook-secret": "test-signature"}
                )
                
                assert response.status_code == 200
                assert response.json() == {"status": "success"}

    def test_sse_endpoint_exists(self):
        """Test that SSE endpoint is accessible."""
        client = TestClient(mcp_app)
        
        # Just verify the endpoint exists and starts streaming
        # We don't need to test the actual streaming in unit tests
        response = client.get("/sse", headers={"Accept": "text/event-stream"})
        assert response.status_code == 200
        assert "text/plain" in response.headers["content-type"]

    def test_webhook_health_check(self):
        """Test webhook server health check."""
        client = TestClient(webhook_app)
        response = client.get("/health")
        assert response.status_code == 200
        assert response.json() == {"status": "healthy"}

    def test_mcp_health_check(self):
        """Test MCP server is accessible."""
        client = TestClient(mcp_app)
        response = client.get("/")
        # FastAPI default returns 404 for root, but server should be running
        # The important thing is we get a response, not a connection error
        assert response.status_code in [200, 404, 405]
