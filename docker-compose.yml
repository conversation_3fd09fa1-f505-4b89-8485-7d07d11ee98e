version: '3.8'

services:
  mono-mcp:
    build: .
    ports:
      - "3000:3000"
      - "8000:8000"
    environment:
      - MONO_SECRET_KEY=${MONO_SECRET_KEY}
      - MONO_WEBHOOK_SECRET=${MONO_WEBHOOK_SECRET}
      - DOMAIN=${DOMAIN}
    volumes:
      - mono_data:/app/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    environment:
      - DOMAIN=${DOMAIN}
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - mono-mcp
    restart: unless-stopped

volumes:
  mono_data:
