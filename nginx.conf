events {
    worker_connections 1024;
}

http {
    upstream mcp {
        server mono-mcp:3000;
    }
    
    upstream webhook {
        server mono-mcp:8000;
    }

    server {
        listen 80;
        server_name ${DOMAIN};
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl;
        server_name ${DOMAIN};
        
        location /mcp/ {
            proxy_pass http://mcp/;
        }

        location /webhook/ {
            proxy_pass http://webhook/;
        }

        location /sse {
            proxy_pass http://mcp/sse;
        }
    }
}
