# Use Python 3.12 slim image
FROM python:3.12-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /bin/uv

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install dependencies
RUN uv sync --frozen --no-cache

# Copy application code
COPY . .

# Install the package
RUN uv pip install -e .

# Create startup script
RUN echo '#!/bin/bash\n\
set -e\n\
echo "Starting MCP server on port 3000..."\n\
uv run uvicorn mono_banking_mcp.server:app --host 0.0.0.0 --port 3000 &\n\
echo "Starting webhook server on port 8000..."\n\
uv run uvicorn mono_banking_mcp.webhook_server:app --host 0.0.0.0 --port 8000 &\n\
wait' > /app/start.sh && chmod +x /app/start.sh

# Expose ports
EXPOSE 3000 8000

# Run the startup script
CMD ["/app/start.sh"]
