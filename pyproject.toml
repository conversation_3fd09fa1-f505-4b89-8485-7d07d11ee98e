[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"


[project]
name = "mono-banking-mcp"
version = "0.1.0"
description = "Mono Banking MCP Server"
authors = [{name = "Your Name", email = "<EMAIL>"}]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastmcp==2.11.3",
    "httpx>=0.25.0",
    "python-dotenv>=1.0.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0"
]


[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-benchmark>=4.0.0",
    "coverage>=7.0.0",
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.5.0"
]

[tool.setuptools.packages.find]
where = ["."]
include = ["mono_banking_mcp*"]
